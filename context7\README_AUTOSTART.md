# 🚀 Context7 开机自启动完整指南

## 📋 当前状态

✅ **Context7 MCP 服务器已成功部署**
- 项目位置：`d:\context7\context7`
- 服务地址：`http://localhost:3000/mcp`
- 健康检查：`http://localhost:3000/ping`

## 🎯 设置开机自启动

### 🔧 方法一：一键设置向导（推荐）

**双击运行** `setup-autostart.bat`

这个向导会引导你选择最适合的自启动方式：

1. **Windows 服务**（推荐）
   - ✅ 真正的系统服务
   - ✅ 开机自动启动
   - ✅ 系统级别管理
   - ✅ 最稳定可靠

2. **PM2 进程管理器**
   - ✅ 轻量级进程管理
   - ✅ 内置日志和监控
   - ✅ 自动重启功能
   - ✅ 跨平台支持

### 🛠️ 方法二：手动设置

#### Windows 服务方式

1. **以管理员身份运行** `install-service-admin.bat`
2. 等待安装完成
3. 服务将自动启动并设置为开机自启动

#### PM2 方式

1. 双击运行 `pm2-setup.bat`
2. 按照提示执行 startup 命令
3. 服务将在后台运行并设置为开机自启动

## 📊 服务管理

### 🎛️ 图形化管理

**双击运行** `manage-service.bat`

提供完整的服务管理功能：
- 🚀 启动服务
- 🛑 停止服务
- 🔄 重启服务
- 📊 查看状态
- 📋 查看日志
- 🔍 测试连接
- 🗑️ 卸载服务

### 💻 命令行管理

#### Windows 服务命令
```cmd
# 查看服务状态
sc query "Context7 MCP Server"

# 启动服务
sc start "Context7 MCP Server"

# 停止服务
sc stop "Context7 MCP Server"
```

#### PM2 命令
```cmd
# 查看状态
pm2 status

# 查看日志
pm2 logs context7-mcp

# 重启服务
pm2 restart context7-mcp

# 停止服务
pm2 stop context7-mcp
```

## 🔍 验证设置

### 1. 检查服务运行状态

访问以下地址验证服务正常：
- `http://localhost:3000/ping` → 应返回 "pong"
- `http://localhost:3000/mcp` → 可访问（可能返回 406，这是正常的）

### 2. 测试开机自启动

1. 重启计算机
2. 等待系统完全启动
3. 访问 `http://localhost:3000/ping` 验证服务自动启动

## 🔧 AI 编辑器集成

服务设置完成后，在你的 AI 编辑器中配置 MCP 连接：

### Cursor
```json
{
  "mcpServers": {
    "context7": {
      "url": "http://localhost:3000/mcp"
    }
  }
}
```

### Claude Code
```bash
claude mcp add --transport http context7 http://localhost:3000/mcp
```

### VS Code
```json
{
  "mcp": {
    "servers": {
      "context7": {
        "type": "http",
        "url": "http://localhost:3000/mcp"
      }
    }
  }
}
```

## 💡 使用示例

在支持 MCP 的 AI 编辑器中使用：

```
创建一个 React Hook Form 表单组件。use context7
```

```
如何在 Next.js 中设置 API 路由？use context7
```

```
使用 Supabase 实现用户认证。use library /supabase/supabase
```

## 🚨 故障排除

### 常见问题

1. **服务安装失败**
   - 确保以管理员身份运行
   - 检查 Node.js 是否正确安装
   - 验证项目路径是否正确

2. **端口被占用**
   - 检查端口 3000 是否被其他程序占用
   - 可以修改端口配置

3. **开机自启动失败**
   - 检查服务是否正确安装
   - 查看系统事件日志
   - 重新运行安装脚本

### 调试步骤

1. **手动测试**
   ```cmd
   cd d:\context7\context7
   node dist/index.js --transport http --port 3000
   ```

2. **检查端口占用**
   ```cmd
   netstat -ano | findstr :3000
   ```

3. **查看服务状态**
   - Windows 服务：`services.msc`
   - PM2：`pm2 status`

## 📁 文件说明

- `setup-autostart.bat` - 一键设置向导
- `manage-service.bat` - 服务管理工具
- `install-service-admin.bat` - Windows 服务安装
- `uninstall-service-admin.bat` - Windows 服务卸载
- `pm2-setup.bat` - PM2 设置脚本
- `ecosystem.config.js` - PM2 配置文件

## 🎉 完成！

设置完成后，Context7 MCP 服务器将：

✅ **开机自动启动**
✅ **后台持续运行**
✅ **自动故障恢复**
✅ **系统级别管理**

现在你可以随时使用 AI 编辑器连接到 Context7 服务，享受准确、最新的代码文档和示例，彻底解决 AI 模型的幻觉问题！

---

**需要帮助？**
- 查看 `SERVICE_SETUP_GUIDE.md` 获取详细技术文档
- 运行 `manage-service.bat` 进行服务管理
- 检查 `logs/` 目录下的日志文件
