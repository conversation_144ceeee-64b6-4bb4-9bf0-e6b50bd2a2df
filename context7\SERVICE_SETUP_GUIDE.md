# Context7 MCP 服务器开机自启动设置指南

## 🎯 目标
将 Context7 MCP 服务器设置为 Windows 系统服务，实现开机自动启动和后台运行。

## 📋 准备工作

确保以下条件已满足：
- ✅ Context7 项目已成功构建（`dist/` 目录存在）
- ✅ Node.js 已安装
- ✅ 具有管理员权限

## 🚀 方法一：Windows 服务（推荐）

### 优点
- 真正的系统服务
- 开机自动启动
- 系统级别的进程管理
- 可通过 Windows 服务管理器控制

### 安装步骤

1. **以管理员身份运行**
   右键点击 `install-service-admin.bat` → "以管理员身份运行"

2. **验证安装**
   - 打开 Windows 服务管理器：`Win + R` → `services.msc`
   - 查找 "Context7 MCP Server" 服务
   - 确认状态为 "正在运行"

3. **测试服务**
   访问 `http://localhost:3000/ping` 应返回 "pong"

### 管理命令

```cmd
# 查看服务状态
sc query "Context7 MCP Server"

# 启动服务
sc start "Context7 MCP Server"

# 停止服务
sc stop "Context7 MCP Server"

# 重启服务
sc stop "Context7 MCP Server" && sc start "Context7 MCP Server"
```

### 卸载服务

以管理员身份运行 `uninstall-service-admin.bat`

## 🔄 方法二：PM2 进程管理器

### 优点
- 轻量级进程管理
- 内置日志管理
- 进程监控和自动重启
- 跨平台支持

### 安装步骤

1. **运行 PM2 设置脚本**
   双击 `pm2-setup.bat`

2. **配置开机自启动**
   按照脚本提示执行 PM2 startup 命令

3. **验证运行**
   ```cmd
   pm2 status
   ```

### PM2 管理命令

```cmd
# 查看状态
pm2 status

# 查看日志
pm2 logs context7-mcp

# 重启服务
pm2 restart context7-mcp

# 停止服务
pm2 stop context7-mcp

# 删除服务
pm2 delete context7-mcp

# 保存当前进程列表
pm2 save

# 重新加载保存的进程
pm2 resurrect
```

## 📊 服务监控

### Windows 服务监控

1. **服务管理器**
   - `Win + R` → `services.msc`
   - 找到 "Context7 MCP Server"
   - 右键 → 属性 → 恢复选项卡 → 设置失败时的操作

2. **事件查看器**
   - `Win + R` → `eventvwr.msc`
   - Windows 日志 → 应用程序
   - 查找 Context7 相关事件

### PM2 监控

```cmd
# 实时监控
pm2 monit

# 查看详细信息
pm2 show context7-mcp

# 查看日志文件位置
pm2 logs context7-mcp --lines 0
```

## 🔧 故障排除

### 常见问题

1. **服务安装失败**
   - 确保以管理员身份运行
   - 检查 Node.js 是否正确安装
   - 验证项目路径是否正确

2. **服务启动失败**
   - 检查端口 3000 是否被占用
   - 查看 Windows 事件日志
   - 验证 `dist/index.js` 文件是否存在

3. **PM2 开机自启动失败**
   - 重新运行 `pm2 startup`
   - 确保以管理员身份执行
   - 检查 PM2 是否正确安装

### 调试步骤

1. **手动测试**
   ```cmd
   cd d:\context7\context7
   node dist/index.js --transport http --port 3000
   ```

2. **检查端口占用**
   ```cmd
   netstat -ano | findstr :3000
   ```

3. **查看服务日志**
   - Windows 服务：事件查看器
   - PM2：`pm2 logs context7-mcp`

## 📁 文件说明

- `install-service.js` - Windows 服务安装脚本
- `uninstall-service.js` - Windows 服务卸载脚本
- `install-service-admin.bat` - 管理员权限安装批处理
- `uninstall-service-admin.bat` - 管理员权限卸载批处理
- `ecosystem.config.js` - PM2 配置文件
- `pm2-setup.bat` - PM2 设置脚本

## ✅ 验证成功

服务成功运行的标志：

1. **HTTP 响应**
   - `http://localhost:3000/ping` 返回 "pong"
   - `http://localhost:3000/mcp` 可访问（虽然可能返回 406）

2. **服务状态**
   - Windows 服务：状态显示 "正在运行"
   - PM2：`pm2 status` 显示 "online"

3. **开机测试**
   - 重启计算机
   - 等待系统完全启动
   - 访问 `http://localhost:3000/ping` 验证服务自动启动

## 🎉 完成！

选择其中一种方法完成设置后，Context7 MCP 服务器将：
- ✅ 开机自动启动
- ✅ 后台持续运行
- ✅ 自动故障恢复
- ✅ 系统级别管理

现在你可以在任何时候使用 AI 编辑器连接到 Context7 服务，享受准确的代码文档和示例！
