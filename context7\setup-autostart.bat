@echo off
title Context7 MCP Server - 开机自启动设置向导
color 0A

echo.
echo ========================================
echo   Context7 MCP Server 开机自启动设置
echo ========================================
echo.
echo 请选择设置方式:
echo.
echo [1] Windows 服务 (推荐)
echo     - 真正的系统服务
echo     - 开机自动启动
echo     - 系统级别管理
echo.
echo [2] PM2 进程管理器
echo     - 轻量级进程管理
echo     - 内置日志和监控
echo     - 跨平台支持
echo.
echo [3] 查看当前状态
echo [4] 退出
echo.

set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto windows_service
if "%choice%"=="2" goto pm2_setup
if "%choice%"=="3" goto check_status
if "%choice%"=="4" goto exit
goto invalid_choice

:windows_service
echo.
echo 正在设置 Windows 服务...
echo ⚠️  需要管理员权限，请在弹出的 UAC 对话框中点击"是"
echo.
pause

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 需要管理员权限，正在重新启动...
    powershell -Command "Start-Process '%~f0' -Verb RunAs -ArgumentList 'service_install'"
    exit /b
)

cd /d "d:\context7\context7"
echo 📦 安装 Windows 服务...
node install-service.js
goto end

:pm2_setup
echo.
echo 正在设置 PM2 进程管理器...
cd /d "d:\context7\context7"

:: 创建日志目录
if not exist "logs" mkdir logs

echo 🚀 启动 Context7 MCP Server...
pm2 start ecosystem.config.js

echo 💾 保存进程列表...
pm2 save

echo 🔧 设置开机自启动...
echo 请在接下来的提示中按照说明执行命令
pm2 startup

echo.
echo ✅ PM2 设置完成！
echo.
echo 管理命令:
echo   pm2 status - 查看状态
echo   pm2 logs context7-mcp - 查看日志
echo   pm2 restart context7-mcp - 重启服务
echo.
goto end

:check_status
echo.
echo 正在检查服务状态...
echo.

:: 检查端口 3000
echo 🔍 检查端口 3000...
netstat -ano | findstr :3000 >nul
if %errorLevel% equ 0 (
    echo ✅ 端口 3000 正在使用中
) else (
    echo ❌ 端口 3000 未被使用
)

:: 检查 Windows 服务
echo.
echo 🔍 检查 Windows 服务...
sc query "Context7 MCP Server" >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Windows 服务已安装
    sc query "Context7 MCP Server"
) else (
    echo ❌ Windows 服务未安装
)

:: 检查 PM2
echo.
echo 🔍 检查 PM2 进程...
pm2 list 2>nul | findstr context7-mcp >nul
if %errorLevel% equ 0 (
    echo ✅ PM2 进程正在运行
    pm2 status
) else (
    echo ❌ PM2 进程未运行
)

:: 测试 HTTP 连接
echo.
echo 🔍 测试 HTTP 连接...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/ping' -TimeoutSec 5; if ($response.Content -eq 'pong') { Write-Host '✅ HTTP 服务正常响应' -ForegroundColor Green } else { Write-Host '❌ HTTP 服务响应异常' -ForegroundColor Red } } catch { Write-Host '❌ 无法连接到 HTTP 服务' -ForegroundColor Red }"

goto end

:invalid_choice
echo.
echo ❌ 无效选择，请重新运行脚本
goto end

:end
echo.
echo 按任意键退出...
pause >nul

:exit
exit /b 0
