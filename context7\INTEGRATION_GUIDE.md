# Context7 本地部署和集成指南

## 项目状态
✅ Context7 MCP 服务器已成功部署并运行在: `http://localhost:3000/mcp`

## 什么是 Context7？
Context7 是一个 MCP (Model Context Protocol) 服务器，专门用于解决 AI 模型的"幻觉"问题。它通过提供最新的、版本特定的文档和代码示例，确保 AI 模型获得准确的信息。

### 主要功能：
- 🔄 实时获取最新的库文档
- 📚 版本特定的 API 文档
- 💡 真实的代码示例
- 🚫 消除过时或虚假的 API 信息

## 集成到 AI 编辑器

### 1. Cursor 集成

#### 方法一：远程服务器连接（推荐）
在 Cursor 设置中添加以下配置到 `~/.cursor/mcp.json`:

```json
{
  "mcpServers": {
    "context7": {
      "url": "http://localhost:3000/mcp"
    }
  }
}
```

#### 方法二：本地命令连接
```json
{
  "mcpServers": {
    "context7": {
      "command": "node",
      "args": ["d:/context7/context7/dist/index.js", "--transport", "http", "--port", "3000"]
    }
  }
}
```

### 2. Claude Desktop 集成

在 Claude Desktop 配置文件 `claude_desktop_config.json` 中添加：

```json
{
  "mcpServers": {
    "Context7": {
      "command": "node",
      "args": ["d:/context7/context7/dist/index.js"]
    }
  }
}
```

### 3. VS Code 集成

在 VS Code MCP 配置中添加：

```json
{
  "mcp": {
    "servers": {
      "context7": {
        "type": "http",
        "url": "http://localhost:3000/mcp"
      }
    }
  }
}
```

### 4. 通用 MCP 客户端集成

对于支持 MCP 的其他 AI 编辑器，使用以下通用配置：

```json
{
  "mcpServers": {
    "context7": {
      "command": "node",
      "args": ["d:/context7/context7/dist/index.js", "--transport", "stdio"]
    }
  }
}
```

## 使用方法

### 基本用法
在你的 AI 编辑器中，只需在提示词中添加 `use context7`：

```
创建一个 Next.js 中间件来检查 JWT token。use context7
```

### 高级用法
如果你知道具体的库 ID，可以直接指定：

```
使用 Supabase 实现用户认证。use library /supabase/supabase for api and docs
```

## 可用工具

Context7 提供两个主要工具：

1. **resolve-library-id**: 将库名称解析为 Context7 兼容的库 ID
2. **get-library-docs**: 获取指定库的文档和代码示例

## 启动和停止服务

### 启动服务器
```bash
cd d:/context7/context7
node dist/index.js --transport http --port 3000
```

### 停止服务器
在运行服务器的终端中按 `Ctrl+C`

## 故障排除

### 常见问题

1. **端口被占用**
   - 服务器会自动尝试下一个可用端口
   - 或者手动指定其他端口：`--port 3001`

2. **模块未找到错误**
   - 确保已运行 `npm install`
   - 确保已运行 `npx tsc` 编译项目

3. **连接问题**
   - 检查防火墙设置
   - 确保服务器正在运行
   - 验证配置文件中的 URL 和端口

### 验证服务器状态
访问 `http://localhost:3000/ping` 应该返回 "pong"

## 解决 AI 模型幻觉问题

Context7 通过以下方式解决 AI 模型的幻觉问题：

1. **实时文档**: 直接从源获取最新文档
2. **版本特定**: 提供特定版本的 API 信息
3. **真实示例**: 提供经过验证的代码示例
4. **上下文注入**: 将准确信息直接注入到 LLM 的上下文中

使用 Context7 后，你将获得：
- ✅ 最新的 API 文档
- ✅ 真实存在的方法和属性
- ✅ 正确的代码示例
- ✅ 版本兼容的解决方案
