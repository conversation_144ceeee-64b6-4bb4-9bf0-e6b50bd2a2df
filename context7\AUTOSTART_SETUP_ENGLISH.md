# Context7 MCP Server - Auto-start Setup Guide

## Current Status
✅ **Context7 MCP Server is successfully deployed**
- Project Location: `d:\context7\context7`
- Server URL: `http://localhost:3000/mcp`
- Health Check: `http://localhost:3000/ping`

## Problem Identified
After computer restart, the service is not automatically starting. We need to set up proper auto-start functionality.

## Solution: Windows Service Setup

### Step 1: Install Windows Service

**IMPORTANT: Must run as Administrator**

1. **Right-click** on `install-windows-service.bat`
2. Select **"Run as administrator"**
3. Click **"Yes"** in the UAC dialog
4. Wait for installation to complete

### Step 2: Verify Installation

1. **Double-click** `test-service.bat` to verify the service is working
2. The test should show:
   - ✅ Windows Service is installed
   - ✅ Port 3000 is in use
   - ✅ Ping test passed
   - ✅ MCP endpoint is accessible

### Step 3: Test Auto-start

1. **Restart your computer**
2. **Wait** for Windows to fully boot
3. **Double-click** `test-service.bat` again
4. **Verify** the service started automatically

## Service Management

### Using Windows Services Manager
1. Press `Win + R`
2. Type `services.msc` and press Enter
3. Find **"Context7MCPServer"**
4. Right-click for options:
   - Start/Stop/Restart
   - Properties → Startup type

### Using Command Line
```cmd
# Check service status
sc query "Context7MCPServer"

# Start service
sc start "Context7MCPServer"

# Stop service
sc stop "Context7MCPServer"
```

### Using Management Script
**Double-click** `manage-service-en.bat` for a menu-driven interface.

## Troubleshooting

### Service Won't Install
- **Cause**: Not running as administrator
- **Solution**: Right-click → "Run as administrator"

### Service Won't Start
- **Cause**: Port 3000 might be in use
- **Solution**: Check what's using port 3000:
  ```cmd
  netstat -ano | findstr :3000
  ```

### Service Doesn't Auto-start After Reboot
- **Cause**: Service startup type not set to Automatic
- **Solution**: 
  1. Open `services.msc`
  2. Find "Context7MCPServer"
  3. Right-click → Properties
  4. Set Startup type to "Automatic"

### Connection Refused After Reboot
- **Cause**: Service failed to start automatically
- **Solution**: 
  1. Run `test-service.bat` to diagnose
  2. Check Windows Event Viewer for errors
  3. Manually start service: `sc start "Context7MCPServer"`

## Files Created (All English Encoded)

- `install-service-en.js` - Service installation script
- `uninstall-service-en.js` - Service removal script
- `install-windows-service.bat` - Administrator installation batch
- `test-service.bat` - Service testing and verification
- `manage-service-en.bat` - Service management interface

## Integration with AI Editors

Once the service is running automatically, configure your AI editor:

### Cursor
```json
{
  "mcpServers": {
    "context7": {
      "url": "http://localhost:3000/mcp"
    }
  }
}
```

### Claude Code
```bash
claude mcp add --transport http context7 http://localhost:3000/mcp
```

### VS Code with MCP Extension
```json
{
  "mcp": {
    "servers": {
      "context7": {
        "type": "http",
        "url": "http://localhost:3000/mcp"
      }
    }
  }
}
```

## Usage Examples

In your AI editor, use these prompts:

```
Create a React form component with validation. use context7
```

```
How to implement authentication in Next.js? use context7
```

```
Show me Supabase database setup examples. use library /supabase/supabase
```

## Success Indicators

✅ **Service is working correctly when:**
- `http://localhost:3000/ping` returns "pong"
- `http://localhost:3000/mcp` is accessible (may return 406, which is normal)
- Service appears as "Running" in Windows Services
- Service starts automatically after computer restart

## Next Steps

1. **Install the service** using `install-windows-service.bat` (as administrator)
2. **Test the installation** using `test-service.bat`
3. **Restart your computer** to verify auto-start
4. **Configure your AI editor** to use the MCP server
5. **Start using Context7** to get accurate, up-to-date code documentation!

---

**Need Help?**
- Run `test-service.bat` to diagnose issues
- Check Windows Services (`services.msc`) for service status
- Look at Windows Event Viewer for detailed error messages
