const Service = require('node-windows').Service;
const path = require('path');

// 创建服务对象（必须与安装时的配置相同）
const svc = new Service({
  name: 'Context7 MCP Server',
  script: path.join(__dirname, 'dist', 'index.js')
});

// 监听卸载事件
svc.on('uninstall', function() {
  console.log('✅ Context7 MCP Server service uninstalled successfully!');
  console.log('The service has been removed from Windows Services.');
});

svc.on('error', function(err) {
  console.error('❌ Service error:', err);
});

// 卸载服务
console.log('🗑️  Uninstalling Context7 MCP Server service...');
console.log('⚠️  This requires administrator privileges.');
svc.uninstall();
