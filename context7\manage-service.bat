@echo off
title Context7 MCP Server - 服务管理
color 0B

:menu
cls
echo.
echo ========================================
echo     Context7 MCP Server 服务管理
echo ========================================
echo.
echo [1] 启动服务
echo [2] 停止服务  
echo [3] 重启服务
echo [4] 查看服务状态
echo [5] 查看服务日志
echo [6] 测试连接
echo [7] 卸载服务
echo [8] 退出
echo.

set /p choice="请选择操作 (1-8): "

if "%choice%"=="1" goto start_service
if "%choice%"=="2" goto stop_service
if "%choice%"=="3" goto restart_service
if "%choice%"=="4" goto status_service
if "%choice%"=="5" goto logs_service
if "%choice%"=="6" goto test_connection
if "%choice%"=="7" goto uninstall_service
if "%choice%"=="8" goto exit
goto invalid_choice

:start_service
echo.
echo 🚀 启动服务...

:: 尝试启动 Windows 服务
sc query "Context7 MCP Server" >nul 2>&1
if %errorLevel% equ 0 (
    sc start "Context7 MCP Server"
    echo ✅ Windows 服务启动命令已发送
) else (
    echo ❌ Windows 服务未安装，尝试启动 PM2...
    pm2 start context7-mcp 2>nul
    if %errorLevel% equ 0 (
        echo ✅ PM2 服务已启动
    ) else (
        echo ❌ 未找到可用的服务
    )
)
goto pause_return

:stop_service
echo.
echo 🛑 停止服务...

:: 尝试停止 Windows 服务
sc query "Context7 MCP Server" >nul 2>&1
if %errorLevel% equ 0 (
    sc stop "Context7 MCP Server"
    echo ✅ Windows 服务停止命令已发送
) else (
    echo ❌ Windows 服务未安装，尝试停止 PM2...
    pm2 stop context7-mcp 2>nul
    if %errorLevel% equ 0 (
        echo ✅ PM2 服务已停止
    ) else (
        echo ❌ 未找到可用的服务
    )
)
goto pause_return

:restart_service
echo.
echo 🔄 重启服务...

:: 尝试重启 Windows 服务
sc query "Context7 MCP Server" >nul 2>&1
if %errorLevel% equ 0 (
    sc stop "Context7 MCP Server"
    timeout /t 3 /nobreak >nul
    sc start "Context7 MCP Server"
    echo ✅ Windows 服务重启命令已发送
) else (
    echo ❌ Windows 服务未安装，尝试重启 PM2...
    pm2 restart context7-mcp 2>nul
    if %errorLevel% equ 0 (
        echo ✅ PM2 服务已重启
    ) else (
        echo ❌ 未找到可用的服务
    )
)
goto pause_return

:status_service
echo.
echo 📊 服务状态...
echo.

:: 检查 Windows 服务
echo 🔍 Windows 服务状态:
sc query "Context7 MCP Server" 2>nul
if %errorLevel% neq 0 (
    echo   ❌ Windows 服务未安装
)

echo.
echo 🔍 PM2 进程状态:
pm2 status 2>nul
if %errorLevel% neq 0 (
    echo   ❌ PM2 未运行或未安装
)

echo.
echo 🔍 端口占用情况:
netstat -ano | findstr :3000
if %errorLevel% neq 0 (
    echo   ❌ 端口 3000 未被占用
)

goto pause_return

:logs_service
echo.
echo 📋 查看日志...

:: 检查是否有 PM2 日志
if exist "logs\combined.log" (
    echo 📄 PM2 日志 (最后 20 行):
    powershell -Command "Get-Content 'logs\combined.log' -Tail 20"
) else (
    echo ❌ 未找到 PM2 日志文件
)

echo.
echo 💡 提示: 
echo   - Windows 服务日志请查看事件查看器 (eventvwr.msc)
echo   - PM2 实时日志: pm2 logs context7-mcp
goto pause_return

:test_connection
echo.
echo 🔍 测试连接...

echo 测试 ping 端点...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/ping' -TimeoutSec 5; Write-Host '✅ Ping: ' $response.Content -ForegroundColor Green } catch { Write-Host '❌ Ping 失败: ' $_.Exception.Message -ForegroundColor Red }"

echo.
echo 测试 MCP 端点...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/mcp' -TimeoutSec 5; Write-Host '✅ MCP 端点可访问 (状态码: ' $response.StatusCode ')' -ForegroundColor Green } catch { if ($_.Exception.Response.StatusCode -eq 406) { Write-Host '✅ MCP 端点正常 (406 是预期响应)' -ForegroundColor Green } else { Write-Host '❌ MCP 端点错误: ' $_.Exception.Message -ForegroundColor Red } }"

goto pause_return

:uninstall_service
echo.
echo ⚠️  确定要卸载服务吗？
echo 这将移除开机自启动设置。
echo.
set /p confirm="输入 Y 确认卸载: "
if /i not "%confirm%"=="Y" goto menu

echo.
echo 🗑️ 卸载服务...

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 需要管理员权限
    echo 请以管理员身份运行此脚本
    goto pause_return
)

:: 卸载 Windows 服务
sc query "Context7 MCP Server" >nul 2>&1
if %errorLevel% equ 0 (
    cd /d "d:\context7\context7"
    node uninstall-service.js
    echo ✅ Windows 服务已卸载
) else (
    echo ❌ Windows 服务未安装
)

:: 停止 PM2 进程
pm2 delete context7-mcp 2>nul
if %errorLevel% equ 0 (
    echo ✅ PM2 进程已删除
    pm2 save
    echo 💾 PM2 配置已保存
)

goto pause_return

:invalid_choice
echo.
echo ❌ 无效选择
goto pause_return

:pause_return
echo.
pause
goto menu

:exit
exit /b 0
