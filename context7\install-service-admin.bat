@echo off
echo Installing Context7 MCP Server as Windows Service...
echo This requires administrator privileges.
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Administrator privileges confirmed.
    echo.
) else (
    echo ❌ This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

:: 切换到项目目录
cd /d "d:\context7\context7"

:: 安装服务
echo 📦 Installing service...
node install-service.js

echo.
echo Installation completed!
echo You can now manage the service through:
echo - Windows Services (services.msc)
echo - Task Manager ^> Services tab
echo - Command: sc query "Context7 MCP Server"
echo.
pause
