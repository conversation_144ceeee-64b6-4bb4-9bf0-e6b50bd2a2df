import { Service } from 'node-windows';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a new service object
const svc = new Service({
  name: 'Context7MCPServer',
  description: 'Context7 MCP Documentation Server for AI Coding Assistants',
  script: path.join(__dirname, 'dist', 'index.js'),
  scriptOptions: '--transport http --port 3000',
  nodeOptions: [
    '--max_old_space_size=4096'
  ],
  env: [
    {
      name: "NODE_ENV",
      value: "production"
    },
    {
      name: "PORT", 
      value: "3000"
    }
  ],
  workingDirectory: __dirname,
  allowServiceLogon: true
});

// Listen for install event
svc.on('install', function() {
  console.log('Context7 MCP Server service installed successfully!');
  console.log('Starting the service...');
  svc.start();
});

svc.on('start', function() {
  console.log('Context7 MCP Server service started successfully!');
  console.log('Server is now running at: http://localhost:3000/mcp');
  console.log('Health check: http://localhost:3000/ping');
  console.log('');
  console.log('The service will now start automatically on system boot.');
  console.log('You can manage the service through Windows Services (services.msc)');
  console.log('Service name: "Context7MCPServer"');
});

svc.on('error', function(err) {
  console.error('Service error:', err);
});

// Install the service
console.log('Installing Context7 MCP Server as Windows service...');
console.log('Script path:', path.join(__dirname, 'dist', 'index.js'));
console.log('Script options: --transport http --port 3000');
console.log('Working directory:', __dirname);
console.log('');

svc.install();
