<service>
	<id>context7mcpserver.exe</id>
	<name>Context7MCPServer</name>
	<description>Context7 MCP Documentation Server for AI Coding Assistants</description>
	<executable>C:\Program Files\nodejs\node.exe</executable>
	<argument>--max_old_space_size=4096</argument>
	<argument>D:\context7\context7\node_modules\node-windows\lib\wrapper.js</argument>
	<argument>--file</argument>
	<argument>D:\context7\context7\dist\index.js</argument>
	<argument>--scriptoptions=--transport http --port 3000</argument>
	<argument>--log</argument>
	<argument>Context7MCPServer wrapper</argument>
	<argument>--grow</argument>
	<argument>0.25</argument>
	<argument>--wait</argument>
	<argument>1</argument>
	<argument>--maxrestarts</argument>
	<argument>3</argument>
	<argument>--abortonerror</argument>
	<argument>n</argument>
	<argument>--stopparentfirst</argument>
	<argument>undefined</argument>
	<logmode>rotate</logmode>
	<stoptimeout>30sec</stoptimeout>
	<env name="NODE_ENV" value="production"/>
	<env name="PORT" value="3000"/>
	<serviceaccount>
		<domain>DESKTOP-VTO2T96</domain>
		<user>LocalSystem</user>
		<password></password>
		<allowservicelogon>true</allowservicelogon>
	</serviceaccount>
	<workingdirectory>D:\context7\context7</workingdirectory>
</service>