@echo off
chcp 65001 >nul
title Context7 MCP Server - Service Test

echo.
echo ========================================
echo     Context7 MCP Server Status Test
echo ========================================
echo.

:: Check Windows Service
echo [1] Checking Windows Service...
sc query "Context7MCPServer" >nul 2>&1
if %errorLevel% equ 0 (
    echo SUCCESS: Windows Service is installed
    sc query "Context7MCPServer"
    echo.
) else (
    echo INFO: Windows Service "Context7MCPServer" is not installed
    echo.
)

:: Check Task Scheduler
echo [1b] Checking Task Scheduler...
schtasks /query /tn "Context7MCPServer" >nul 2>&1
if %errorLevel% equ 0 (
    echo SUCCESS: Scheduled Task is configured
    schtasks /query /tn "Context7MCPServer" /fo LIST
    echo.
) else (
    echo INFO: Scheduled Task "Context7MCPServer" is not configured
    echo.
)

:: Check port usage
echo [2] Checking port 3000...
netstat -ano | findstr :3000 >nul
if %errorLevel% equ 0 (
    echo SUCCESS: Port 3000 is in use
    netstat -ano | findstr :3000
    echo.
) else (
    echo ERROR: Port 3000 is not in use
    echo.
)

:: Test HTTP connection
echo [3] Testing HTTP connection...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/ping' -TimeoutSec 5; if ($response.Content -eq 'pong') { Write-Host 'SUCCESS: Ping test passed - Service is working!' -ForegroundColor Green } else { Write-Host 'ERROR: Ping test failed - Unexpected response' -ForegroundColor Red } } catch { Write-Host 'ERROR: Cannot connect to service' -ForegroundColor Red; Write-Host 'Details: ' $_.Exception.Message -ForegroundColor Red }"

echo.
echo [4] Testing MCP endpoint...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/mcp' -TimeoutSec 5; Write-Host 'SUCCESS: MCP endpoint is accessible' -ForegroundColor Green } catch { if ($_.Exception.Response.StatusCode -eq 406) { Write-Host 'SUCCESS: MCP endpoint is working (406 response is normal)' -ForegroundColor Green } else { Write-Host 'ERROR: MCP endpoint error' -ForegroundColor Red; Write-Host 'Details: ' $_.Exception.Message -ForegroundColor Red } }"

echo.
echo ========================================
echo                Summary
echo ========================================

:: Final status check
sc query "Context7MCPServer" | findstr "RUNNING" >nul 2>&1
if %errorLevel% equ 0 (
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/ping' -TimeoutSec 3; if ($response.Content -eq 'pong') { Write-Host 'OVERALL STATUS: SERVICE IS WORKING CORRECTLY!' -ForegroundColor Green -BackgroundColor Black; Write-Host ''; Write-Host 'Your Context7 MCP Server is ready to use:' -ForegroundColor Cyan; Write-Host '  - MCP Server: http://localhost:3000/mcp' -ForegroundColor White; Write-Host '  - Health Check: http://localhost:3000/ping' -ForegroundColor White; Write-Host '  - Auto-start: Enabled (will start on boot)' -ForegroundColor White } else { Write-Host 'OVERALL STATUS: SERVICE HAS ISSUES' -ForegroundColor Red -BackgroundColor Black } } catch { Write-Host 'OVERALL STATUS: SERVICE IS NOT RESPONDING' -ForegroundColor Red -BackgroundColor Black }"
) else (
    echo OVERALL STATUS: SERVICE IS NOT RUNNING
    echo.
    echo Troubleshooting:
    echo 1. Try running: setup-task-scheduler.bat as administrator
    echo 2. Check Windows Services ^(services.msc^) for "Context7MCPServer"
    echo 3. Check Task Scheduler ^(taskschd.msc^) for "Context7MCPServer"
    echo 4. Try manual start: start-context7-service.bat
)

echo.
pause
