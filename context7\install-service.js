const Service = require('node-windows').Service;
const path = require('path');

// 创建一个新的服务对象
const svc = new Service({
  name: 'Context7 MCP Server',
  description: 'Context7 MCP Server - Up-to-date code documentation for LLMs and AI code editors',
  script: path.join(__dirname, 'dist', 'index.js'),
  scriptOptions: '--transport http --port 3000',
  nodeOptions: [
    '--max_old_space_size=4096'
  ],
  env: [
    {
      name: "NODE_ENV",
      value: "production"
    },
    {
      name: "PORT",
      value: "3000"
    }
  ],
  workingDirectory: __dirname,
  allowServiceLogon: true
});

// 监听安装事件
svc.on('install', function() {
  console.log('✅ Context7 MCP Server service installed successfully!');
  console.log('🚀 Starting the service...');
  svc.start();
});

svc.on('start', function() {
  console.log('✅ Context7 MCP Server service started successfully!');
  console.log('🌐 Server is now running at: http://localhost:3000/mcp');
  console.log('🔍 Health check: http://localhost:3000/ping');
  console.log('');
  console.log('The service will now start automatically on system boot.');
  console.log('You can manage the service through Windows Services (services.msc)');
});

svc.on('error', function(err) {
  console.error('❌ Service error:', err);
});

// 安装服务
console.log('📦 Installing Context7 MCP Server as Windows Service...');
console.log('⚠️  This requires administrator privileges.');
svc.install();
