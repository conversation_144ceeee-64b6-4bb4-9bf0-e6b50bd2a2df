@echo off
chcp 65001 >nul
title Context7 MCP Server - Task Scheduler Setup

echo.
echo ========================================
echo   Context7 MCP Server Auto-start Setup
echo   Using Windows Task Scheduler
echo ========================================
echo.

:: Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Administrator privileges required!
    echo.
    echo Please follow these steps:
    echo 1. Right-click on this file: setup-task-scheduler.bat
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" in the UAC dialog
    echo.
    pause
    exit /b 1
)

echo Administrator privileges confirmed.
echo.

:: Delete existing task if it exists
echo Removing any existing task...
schtasks /delete /tn "Context7MCPServer" /f >nul 2>&1

:: Create the scheduled task
echo Creating scheduled task for auto-start...
schtasks /create /tn "Context7MCPServer" /tr "\"d:\context7\context7\start-context7-service.bat\"" /sc onstart /ru "SYSTEM" /rl highest /f

if %errorLevel% neq 0 (
    echo ERROR: Failed to create scheduled task!
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo SUCCESS: Scheduled task created successfully!
echo.

:: Start the task immediately
echo Starting the service now...
schtasks /run /tn "Context7MCPServer"

echo.
echo Waiting for service to start...
timeout /t 10 /nobreak >nul

:: Test the service
echo Testing service connection...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/ping' -TimeoutSec 10; if ($response.Content -eq 'pong') { Write-Host 'SUCCESS: Service is responding correctly!' -ForegroundColor Green; Write-Host 'Server URL: http://localhost:3000/mcp' -ForegroundColor Cyan; Write-Host 'Health Check: http://localhost:3000/ping' -ForegroundColor Cyan } else { Write-Host 'WARNING: Service responded but with unexpected content' -ForegroundColor Yellow } } catch { Write-Host 'ERROR: Service is not responding. Please check the logs.' -ForegroundColor Red }"

echo.
echo Setup completed!
echo.
echo Task Information:
echo   - Task Name: Context7MCPServer
echo   - Trigger: At system startup
echo   - Run as: SYSTEM account
echo   - Priority: Highest
echo.
echo Management:
echo   - View tasks: Win + R, type "taskschd.msc"
echo   - Find task: "Context7MCPServer"
echo   - You can enable/disable/run the task from there
echo.
echo The service will now start automatically when Windows boots!
echo.
pause
