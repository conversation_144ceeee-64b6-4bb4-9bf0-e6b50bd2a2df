# Context7 MCP Server 任务计划设置脚本
# 需要管理员权限运行

Write-Host "设置 Context7 MCP Server 开机自启动任务..." -ForegroundColor Green

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInR<PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误: 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击 PowerShell 并选择 '以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按 Enter 键退出"
    exit 1
}

try {
    # 删除现有任务（如果存在）
    $taskName = "Context7 MCP Server"
    $existingTask = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
    if ($existingTask) {
        Write-Host "删除现有任务..." -ForegroundColor Yellow
        Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
    }

    # 创建任务动作
    $action = New-ScheduledTaskAction -Execute "cmd.exe" -Argument "/c `"d:\context7\context7\start-context7-boot.bat`""

    # 创建任务触发器（系统启动时）
    $trigger = New-ScheduledTaskTrigger -AtStartup

    # 设置任务以系统账户运行
    $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest

    # 创建任务设置
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -RestartCount 3 -RestartInterval (New-TimeSpan -Minutes 1)

    # 注册任务
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description "Context7 MCP Server 开机自启动服务"

    Write-Host "✅ 任务计划设置成功!" -ForegroundColor Green
    Write-Host "Context7 MCP Server 将在系统启动时自动运行" -ForegroundColor Green
    
    # 显示任务信息
    Write-Host "`n任务信息:" -ForegroundColor Cyan
    Get-ScheduledTask -TaskName $taskName | Format-Table TaskName, State, LastRunTime, NextRunTime -AutoSize

    Write-Host "`n你可以通过以下方式管理任务:" -ForegroundColor Yellow
    Write-Host "1. 任务计划程序: Win+R -> taskschd.msc" -ForegroundColor White
    Write-Host "2. PowerShell: Get-ScheduledTask -TaskName '$taskName'" -ForegroundColor White
    Write-Host "3. 删除任务: Unregister-ScheduledTask -TaskName '$taskName'" -ForegroundColor White

} catch {
    Write-Host "❌ 设置任务计划时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n按 Enter 键退出..." -ForegroundColor Gray
Read-Host
