@echo off
title Context7 MCP Server - 设置开机自启动
color 0A

echo.
echo ========================================
echo   Context7 MCP Server 开机自启动设置
echo ========================================
echo.
echo 正在设置 Windows 任务计划...
echo ⚠️  需要管理员权限
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 需要管理员权限
    echo 正在请求管理员权限...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo ✅ 管理员权限确认
echo.

:: 运行 PowerShell 脚本
powershell -ExecutionPolicy Bypass -File "setup-task-scheduler.ps1"

echo.
echo 设置完成！
pause
