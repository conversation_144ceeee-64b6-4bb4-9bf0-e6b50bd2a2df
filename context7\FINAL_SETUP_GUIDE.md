# Context7 MCP Server - Final Auto-start Setup Guide

## Current Status
✅ **Context7 MCP Server is working correctly when started manually**
❌ **Auto-start on boot is not configured**

## Problem Identified
- The server runs perfectly when started manually: `node dist/index.js --transport http --port 3000`
- Windows Service installation had issues with ES modules
- Need a reliable auto-start solution

## Solution: Windows Task Scheduler (Recommended)

### Why Task Scheduler?
- ✅ More reliable than Windows Services for Node.js apps
- ✅ Easier to configure and troubleshoot
- ✅ Better logging and error handling
- ✅ Works with ES modules without issues

### Step 1: Install Auto-start

**IMPORTANT: Must run as Administrator**

1. **Right-click** on `setup-task-scheduler.bat`
2. Select **"Run as administrator"**
3. Click **"Yes"** in the UAC dialog
4. Wait for setup to complete

The script will:
- Create a Windows scheduled task
- Set it to run at system startup
- Start the service immediately
- Test the connection

### Step 2: Verify Installation

1. **Double-click** `test-service.bat` to verify everything is working
2. You should see:
   - ✅ Scheduled Task is configured
   - ✅ Port 3000 is in use
   - ✅ Ping test passed
   - ✅ MCP endpoint is accessible
   - ✅ OVERALL STATUS: SERVICE IS WORKING CORRECTLY!

### Step 3: Test Auto-start

1. **Restart your computer**
2. **Wait** for Windows to fully boot (about 1-2 minutes)
3. **Open browser** and go to `http://localhost:3000/ping`
4. **Should return** "pong" immediately

## Alternative: Manual Start (If needed)

If you need to start the service manually:

1. **Double-click** `start-context7-service.bat`
2. Keep the window open (minimizing is OK)
3. The service will run until you close the window

## Service Management

### Using Task Scheduler
1. Press `Win + R`
2. Type `taskschd.msc` and press Enter
3. Find **"Context7MCPServer"** in the task list
4. Right-click for options:
   - Run/Stop the task
   - Enable/Disable auto-start
   - View task properties and logs

### Using Command Line
```cmd
# Run the task (start service)
schtasks /run /tn "Context7MCPServer"

# Stop the task
schtasks /end /tn "Context7MCPServer"

# Check task status
schtasks /query /tn "Context7MCPServer"
```

### Remove Auto-start
If you want to remove auto-start:
1. **Right-click** `remove-task-scheduler.bat`
2. Select **"Run as administrator"**

## Troubleshooting

### Service Won't Start After Setup
1. **Check Task Scheduler**: `taskschd.msc` → Find "Context7MCPServer"
2. **Check logs**: Look in `d:\context7\context7\logs\service.log`
3. **Test manually**: Run `start-context7-service.bat`

### Connection Refused After Reboot
1. **Wait longer**: Service might take 1-2 minutes to start after boot
2. **Check task status**: `schtasks /query /tn "Context7MCPServer"`
3. **Restart task**: `schtasks /run /tn "Context7MCPServer"`

### Port Already in Use
1. **Check what's using port 3000**: `netstat -ano | findstr :3000`
2. **Kill the process**: `taskkill /PID [PID_NUMBER] /F`
3. **Restart the service**: `schtasks /run /tn "Context7MCPServer"`

## Files Overview

- `setup-task-scheduler.bat` - **Main setup script** (run as admin)
- `start-context7-service.bat` - Manual start script
- `test-service.bat` - Test and verify service status
- `remove-task-scheduler.bat` - Remove auto-start (run as admin)

## Integration with AI Editors

Once auto-start is working, configure your AI editor:

### Cursor
```json
{
  "mcpServers": {
    "context7": {
      "url": "http://localhost:3000/mcp"
    }
  }
}
```

### Claude Code
```bash
claude mcp add --transport http context7 http://localhost:3000/mcp
```

### VS Code
```json
{
  "mcp": {
    "servers": {
      "context7": {
        "type": "http",
        "url": "http://localhost:3000/mcp"
      }
    }
  }
}
```

## Success Indicators

✅ **Everything is working when:**
- `http://localhost:3000/ping` returns "pong" immediately after boot
- Task Scheduler shows "Context7MCPServer" as "Ready" or "Running"
- `test-service.bat` shows "OVERALL STATUS: SERVICE IS WORKING CORRECTLY!"
- Service starts automatically after computer restart

## Next Steps

1. **Run setup**: `setup-task-scheduler.bat` as administrator
2. **Test immediately**: `test-service.bat`
3. **Test auto-start**: Restart computer and verify
4. **Configure AI editor**: Add MCP server configuration
5. **Start using**: Enjoy accurate, up-to-date code documentation!

---

**Need Help?**
- Run `test-service.bat` to diagnose any issues
- Check Task Scheduler (`taskschd.msc`) for task status
- Look at `logs\service.log` for detailed error messages
- Try manual start with `start-context7-service.bat`
