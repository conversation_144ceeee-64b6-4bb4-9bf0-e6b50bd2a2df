@echo off
chcp 65001 >nul
title Context7 MCP Server - Remove Auto-start

echo.
echo ========================================
echo   Remove Context7 MCP Server Auto-start
echo ========================================
echo.

:: Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Administrator privileges required!
    echo Please right-click this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Administrator privileges confirmed.
echo.

:: Stop the task if running
echo Stopping the service...
schtasks /end /tn "Context7MCPServer" >nul 2>&1

:: Delete the scheduled task
echo Removing scheduled task...
schtasks /delete /tn "Context7MCPServer" /f

if %errorLevel% equ 0 (
    echo SUCCESS: Scheduled task removed successfully!
    echo The service will no longer start automatically on boot.
) else (
    echo ERROR: Failed to remove scheduled task or task was not found.
)

echo.
pause
