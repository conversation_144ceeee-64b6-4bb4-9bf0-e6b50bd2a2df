import { Service } from 'node-windows';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a service object
const svc = new Service({
  name: 'Context7MCPServer',
  script: path.join(__dirname, 'dist', 'index.js')
});

// Listen for uninstall event
svc.on('uninstall', function() {
  console.log('Context7 MCP Server service uninstalled successfully!');
  console.log('The service will no longer start automatically on system boot.');
});

svc.on('error', function(err) {
  console.error('Service error:', err);
});

// Uninstall the service
console.log('Uninstalling Context7 MCP Server service...');
console.log('Service name: "Context7MCPServer"');
console.log('');

svc.uninstall();
