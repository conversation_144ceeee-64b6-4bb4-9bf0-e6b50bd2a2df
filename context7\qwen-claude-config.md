# Qwen Coder 和 Claude Code 集成配置

## Claude Code 集成

Claude Code 是 Anthropic 官方的代码编辑器，支持 MCP 协议。

### 安装和配置

1. **安装 Claude Code**（如果尚未安装）
2. **添加 MCP 服务器**

#### 方法一：使用命令行添加（推荐）
```bash
claude mcp add --transport http context7 http://localhost:3000/mcp
```

#### 方法二：手动配置
在 Claude Code 的配置文件中添加：
```json
{
  "mcpServers": {
    "context7": {
      "url": "http://localhost:3000/mcp"
    }
  }
}
```

### 使用示例
在 Claude Code 中，你可以这样使用：

```
请帮我创建一个使用 React Hook Form 的表单组件。use context7
```

```
如何在 Next.js 中设置 API 路由？use context7
```

## Qwen Coder 集成

Qwen Coder 是阿里云推出的 AI 代码助手。如果 Qwen Coder 支持 MCP 协议，可以按以下方式配置：

### 配置方法

1. **检查 MCP 支持**
   首先确认你的 Qwen Coder 版本是否支持 MCP 协议

2. **添加 MCP 服务器配置**
   在 Qwen Coder 的设置中添加：
   ```json
   {
     "mcpServers": {
       "context7": {
         "url": "http://localhost:3000/mcp"
       }
     }
   }
   ```

3. **或者使用本地命令方式**
   ```json
   {
     "mcpServers": {
       "context7": {
         "command": "node",
         "args": ["d:/context7/context7/dist/index.js"]
       }
     }
   }
   ```

### 使用示例
```
帮我写一个 Vue 3 组合式 API 的组件。use context7
```

```
如何使用 TypeScript 配置 Express 服务器？use context7
```

## 通用 MCP 客户端配置

如果你使用的 AI 编辑器支持 MCP 但不在上述列表中，可以尝试以下通用配置：

### HTTP 传输方式
```json
{
  "mcpServers": {
    "context7": {
      "type": "http",
      "url": "http://localhost:3000/mcp"
    }
  }
}
```

### STDIO 传输方式
```json
{
  "mcpServers": {
    "context7": {
      "command": "node",
      "args": ["d:/context7/context7/dist/index.js", "--transport", "stdio"]
    }
  }
}
```

### SSE 传输方式
```json
{
  "mcpServers": {
    "context7": {
      "type": "sse",
      "url": "http://localhost:3000/sse"
    }
  }
}
```

## 验证配置

### 1. 检查服务器状态
访问 `http://localhost:3000/ping` 应该返回 "pong"

### 2. 测试 MCP 连接
在你的 AI 编辑器中尝试以下提示：
```
resolve library id for "react"
```

### 3. 测试文档获取
```
get library docs for "/facebook/react"
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 确保 Context7 服务器正在运行
   - 检查端口是否正确（默认 3000）

2. **MCP 协议不支持**
   - 确认你的 AI 编辑器版本支持 MCP
   - 查看编辑器的 MCP 文档

3. **配置文件位置**
   - 不同的 AI 编辑器配置文件位置可能不同
   - 查看具体编辑器的文档

### 调试技巧

1. **查看服务器日志**
   在运行 Context7 的终端中查看输出日志

2. **使用 MCP Inspector**
   ```bash
   npx @modelcontextprotocol/inspector http://localhost:3000/mcp
   ```

3. **测试工具可用性**
   确保 `resolve-library-id` 和 `get-library-docs` 工具可用

## 最佳实践

1. **始终先启动 Context7 服务器**
   在使用 AI 编辑器之前确保服务器运行

2. **使用具体的库名称**
   ```
   使用 "@supabase/supabase-js" 而不是 "supabase"
   ```

3. **指定版本（如果需要）**
   ```
   获取 Next.js 14 的文档。use context7
   ```

4. **组合使用**
   ```
   创建一个使用 Prisma ORM 和 PostgreSQL 的用户模型。use context7
   ```
