@echo off
title Context7 MCP Server - 设置开机自启动（启动文件夹方式）
color 0A

echo.
echo ========================================
echo   Context7 MCP Server 开机自启动设置
echo ========================================
echo.
echo 使用 Windows 启动文件夹方式设置开机自启动
echo.

:: 获取启动文件夹路径
set "startupFolder=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"

echo 启动文件夹路径: %startupFolder%
echo.

:: 创建启动脚本的快捷方式
echo 正在创建启动脚本...

:: 创建一个 VBS 脚本来创建快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%startupFolder%\Context7 MCP Server.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "d:\context7\context7\start-context7-boot.bat" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "d:\context7\context7" >> CreateShortcut.vbs
echo oLink.Description = "Context7 MCP Server 开机自启动" >> CreateShortcut.vbs
echo oLink.WindowStyle = 7 >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

:: 运行 VBS 脚本
cscript CreateShortcut.vbs >nul

:: 删除临时 VBS 文件
del CreateShortcut.vbs >nul

echo ✅ 开机自启动设置完成！
echo.
echo 📁 快捷方式已创建在启动文件夹中
echo 📍 位置: %startupFolder%\Context7 MCP Server.lnk
echo.
echo 🔄 现在重启计算机测试自动启动功能
echo 🌐 重启后访问 http://localhost:3000/ping 验证服务
echo.

:: 询问是否打开启动文件夹
set /p openFolder="是否打开启动文件夹查看？(Y/N): "
if /i "%openFolder%"=="Y" (
    explorer "%startupFolder%"
)

echo.
echo 按任意键退出...
pause >nul
