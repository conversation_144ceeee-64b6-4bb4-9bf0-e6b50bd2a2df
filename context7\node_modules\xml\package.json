{"name": "xml", "version": "1.0.1", "description": "Fast and simple xml generator. Supports attributes, CDATA, etc. Includes tests and examples.", "homepage": "http://github.com/dylang/node-xml", "keywords": ["xml", "create", "builder", "json", "simple"], "author": "<PERSON> (https://github.com/dylang)", "contributors": ["<PERSON> (https://github.com/dylang)", "Dodo (https://github.com/dodo)", "<PERSON> (<EMAIL>)", "<PERSON><PERSON><PERSON>", "carolineBda (https://github.com/carolineBda)", "<PERSON> https://github.com/evantill", "<PERSON> https://github.com/reywood"], "repository": {"type": "git", "url": "http://github.com/dylang/node-xml"}, "bugs": {"url": "http://github.com/dylang/node-xml/issues"}, "devDependencies": {"ava": "^0.11.0"}, "scripts": {"test": "ava"}, "main": "lib/xml.js", "license": "MIT"}