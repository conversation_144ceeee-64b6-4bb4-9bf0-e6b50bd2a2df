@echo off
echo Setting up Context7 MCP Server with PM2...
echo.

cd /d "d:\context7\context7"

:: 创建日志目录
if not exist "logs" mkdir logs

:: 启动应用
echo 🚀 Starting Context7 MCP Server with PM2...
pm2 start ecosystem.config.js

:: 保存 PM2 进程列表
echo 💾 Saving PM2 process list...
pm2 save

:: 设置开机自启动
echo 🔧 Setting up startup script...
pm2 startup

echo.
echo ✅ Setup completed!
echo.
echo Available commands:
echo   pm2 status          - Check service status
echo   pm2 logs context7-mcp - View logs
echo   pm2 restart context7-mcp - Restart service
echo   pm2 stop context7-mcp - Stop service
echo   pm2 delete context7-mcp - Remove service
echo.
echo 🌐 Server should be running at: http://localhost:3000/mcp
echo 🔍 Health check: http://localhost:3000/ping
echo.
pause
