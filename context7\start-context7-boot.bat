@echo off
REM Context7 MCP Server 开机启动脚本
REM 等待系统完全启动
timeout /t 30 /nobreak >nul

REM 切换到项目目录
cd /d "d:\context7\context7"

REM 启动 PM2 守护进程
pm2 ping >nul 2>&1
if %errorLevel% neq 0 (
    pm2 kill >nul 2>&1
    timeout /t 5 /nobreak >nul
)

REM 恢复保存的进程
pm2 resurrect >nul 2>&1

REM 如果没有进程在运行，启动 Context7
pm2 list | findstr context7-mcp >nul
if %errorLevel% neq 0 (
    pm2 start ecosystem.config.cjs >nul 2>&1
    pm2 save >nul 2>&1
)

REM 记录启动日志
echo %date% %time% - Context7 MCP Server startup completed >> logs\startup.log
